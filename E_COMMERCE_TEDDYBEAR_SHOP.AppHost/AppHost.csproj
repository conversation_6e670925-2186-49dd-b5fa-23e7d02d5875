<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>b8b8b8b8-b8b8-b8b8-b8b8-b8b8b8b8b8b8</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" />
    <PackageReference Include="Aspire.Hosting.Azure.AppService" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults\ServiceDefaults.csproj" />
    <ProjectReference Include="..\AUTHORIZATION\AUTHORIZATION.API\AUTHORIZATION.API.csproj" />
    <ProjectReference Include="..\COMMAND\COMMAND.API\COMMAND.API.csproj" />
    <ProjectReference Include="..\QUERY\QUERY.API\QUERY.API.csproj" />
  </ItemGroup>

</Project>
