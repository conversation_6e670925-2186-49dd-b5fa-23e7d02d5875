<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Carter" />
        <PackageReference Include="MicroElements.Swashbuckle.FluentValidation" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
        <PackageReference Include="Microsoft.CodeAnalysis.Common" />
        <PackageReference Include="Microsoft.CodeAnalysis.CSharp" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Swashbuckle.AspNetCore" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" />
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\CONTRACT\CONTRACT\CONTRACT.csproj"/>
        <ProjectReference Include="..\COMMAND.APPLICATION\COMMAND.APPLICATION.csproj"/>
        <ProjectReference Include="..\COMMAND.CONTRACT\COMMAND.CONTRACT.csproj"/>
        <ProjectReference Include="..\COMMAND.INFRASTRUCTURE\COMMAND.INFRASTRUCTURE.csproj"/>
        <ProjectReference Include="..\COMMAND.PRESENTATION\COMMAND.PRESENTATION.csproj"/>
        <ProjectReference Include="..\COMMAND.PERSISTENCE\COMMAND.PERSISTENCE.csproj"/>
        <ProjectReference Include="..\..\E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults\ServiceDefaults.csproj"/>
    </ItemGroup>

</Project>
