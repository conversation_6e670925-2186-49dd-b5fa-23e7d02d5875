<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="MicroElements.Swashbuckle.FluentValidation" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
        <PackageReference Include="Swashbuckle.AspNetCore" />
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\CONTRACT\CONTRACT\CONTRACT.csproj"/>
        <ProjectReference Include="..\..\E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults\ServiceDefaults.csproj"/>
        <ProjectReference Include="..\QUERY.APPLICATION\QUERY.APPLICATION.csproj"/>
        <ProjectReference Include="..\QUERY.CONTRACT\QUERY.CONTRACT.csproj"/>
        <ProjectReference Include="..\QUERY.INFRASTRUCTURE\QUERY.INFRASTRUCTURE.csproj"/>
        <ProjectReference Include="..\QUERY.PERSISTENCE\QUERY.PERSISTENCE.csproj"/>
        <ProjectReference Include="..\QUERY.PRESENTATION\QUERY.PRESENTATION.csproj"/>
    </ItemGroup>

</Project>
