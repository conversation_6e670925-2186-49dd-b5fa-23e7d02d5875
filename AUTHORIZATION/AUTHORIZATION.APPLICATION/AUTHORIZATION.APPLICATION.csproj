<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\COMMAND\COMMAND.PERSISTENCE\COMMAND.PERSISTENCE.csproj" />
        <ProjectReference Include="..\..\CONTRACT\CONTRACT\CONTRACT.csproj"/>
        <ProjectReference Include="..\AUTHORIZATION.CONTRACT\AUTHORIZATION.CONTRACT.csproj"/>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="BCrypt" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="DependencyInjection\Extensions\" />
      <Folder Include="Exceptions\" />
    </ItemGroup>

</Project>
