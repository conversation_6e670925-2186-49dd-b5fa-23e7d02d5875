{"version": 2, "dependencies": {"net8.0": {"Ardalis.SmartEnum": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "B5pGgXHSNdWgCqy7vhTaTH44F3HRtTXgiQUz0rziDvjpHCx5FYJiA9Tcwh5ApFCZXyFYS3Tf16RCEcf/+HSzDg=="}, "Asp.Versioning.Abstractions": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "Kc4g1a4Wo7W7cVrPF5F5JzSOFItZH7jQiVEX8DvCmJ5TXe0i1LotZhzbqBMUYRkhrivR+GPnlceh9E/xohJlDQ==", "dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Asp.Versioning.Http": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "V+4MsRXzn26FBzLXhqNNfAJJfyCAwqLJZNjYu/vrZKZ2W3q24R8MuFXZUdq27cLLlVn3In0ngdMXFoFxLgCQvw==", "dependencies": {"Asp.Versioning.Abstractions": "6.0.0"}}, "Asp.Versioning.Mvc": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "fMcYYcGUvIe9UfQv3RfmPvmk64f3n4QutZll4c2jIqmVqax7r1M7yiHY7v9kLolSO9bYqP4xhhS3I3elbz0m+A==", "dependencies": {"Asp.Versioning.Http": "6.0.0"}}, "Asp.Versioning.Mvc.ApiExplorer": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "5pjMp6M/EAojKTSN/vJEx57gEATrHZfk88g4SXNKl5i5f3E19+oezEbYfqkyVT7c2Aj026mK79lF4kJ0qNkZ3w==", "dependencies": {"Asp.Versioning.Mvc": "6.0.0"}}, "Carter": {"type": "Transitive", "resolved": "3.5.0", "contentHash": "IA0mmw2PB00okg1JcNZweOJaKsX91tV0Gv8gJv1O9RqcT8E3LiWC7yATF8gpzanYyYTpHiwnhQm7m0R1ZolJyA==", "dependencies": {"FluentValidation": "7.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.0.0", "Microsoft.AspNetCore.Routing": "2.0.0", "Microsoft.Extensions.DependencyInjection": "2.0.0", "Microsoft.Extensions.DependencyModel": "2.0.0", "Newtonsoft.Json": "10.0.3", "System.ValueTuple": "4.4.0"}}, "CloudinaryDotNet": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "RDvivwwleq5MyiuS+AYQE8rgrC1nbPj/VzpXy48yL1axv24jwHdYF8ldn8s23cypamkivdkXcYsRA0oW9YkmwQ=="}, "FluentValidation": {"type": "Transitive", "resolved": "1.3.0", "contentHash": "MOJrooRXbKmOxMSr+hYJJIUMDZ/ZWgZkNaQdanr8zI8IXE+LGriola3tYgEuGQvndfQQVZHH9FCa62BauxqULA=="}, "FluentValidation.DependencyInjectionExtensions": {"type": "Transitive", "resolved": "8.2.2", "contentHash": "WGR6uZVCnVZscR3UZS8eIf2D19ETySMAq48e5qdUTCktGEYMg6FaFw7H3cFoeVM1ga/EV6aoZVa1DHlIBtJ+sg==", "dependencies": {"FluentValidation": "8.2.2", "Microsoft.Extensions.Dependencyinjection.Abstractions": "2.1.0"}}, "MailKit": {"type": "Transitive", "resolved": "0.1.0", "contentHash": "UwR5/exHrMWU2c224jCuEnHEf4MK1RwWS6+uWBQQCNilz/CwRWUH3HPbOaZNsIdPb9YKCWqe1JKK5A0ba0sdrQ==", "dependencies": {"MimeKit": "0.21.0"}}, "MediatR": {"type": "Transitive", "resolved": "0.1.0", "contentHash": "+dQJkXhd9NVnsalQzBgN7euJg/Q0GtRBQu9V8Lnz9Bfex9NpbUYsfrYqLn/SfcNOxa2pDPHPrCeQUSltzue7Cw==", "dependencies": {"Portable.CommonServiceLocator": "1.2.2"}}, "Microsoft.AspNetCore.Authentication": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "3af/pZSoaIkZPjGPTlpvIXKDA4NCN6O7++iSO4N2rnKjC5OFHnCX5CLzRxO6721kqovIssRPo84q1HvT4IL6GQ==", "dependencies": {"Microsoft.AspNetCore.DataProtection": "1.0.0", "Microsoft.AspNetCore.Http": "1.0.0", "Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "Microsoft.Extensions.WebEncoders": "1.0.0", "System.Net.Http": "4.1.0"}}, "Microsoft.AspNetCore.Authentication.Google": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "Y11elEMu4lP5qFnQ0UGbpe8d0uOlaT1KLkgDiRKpPvy1MqmtgPtVjqzvX9GG6+dJ187xY8N7Y1gWlTyAZh7rhg==", "dependencies": {"Microsoft.AspNetCore.Authentication.OAuth": "1.0.0"}}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "CSZ24QCdMf5ItzaSA9wPe+UX3/JDRY8MQOQ/6gSYfZQ/3dfYXpdqA4Q+EG6ETdGXICWCwOSgG5hSLt7/SivRZg==", "dependencies": {"Microsoft.AspNetCore.Authentication": "1.0.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "2.0.0"}}, "Microsoft.AspNetCore.Authentication.OAuth": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "QZfUgJPv/jnFCZ+CJOSeAXzhYQVBe864krJcijDfTE0dspuF1+uifRJDaCuHGKSJlnsyOGVGjrBcMZLK3wnCkg==", "dependencies": {"Microsoft.AspNetCore.Authentication": "1.0.0", "Newtonsoft.Json": "9.0.1", "System.Runtime.Serialization.Primitives": "4.1.1"}}, "Microsoft.AspNetCore.Authorization": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "iVFQ5xHSyxmfWYdl5B/xIFzXgm4SRgYQUKlLFVNGfEhbbjw0Ur2pfVrEvpENrhHFOQ2XAZcuFlGxSIzZwsVrMg==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Security.Claims": "4.0.1"}}, "Microsoft.AspNetCore.Cryptography.Internal": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "0btvxwOqYNpKTUQrD7LA3p6Wi0vrhfWGBVqIKPS1KtEdkCv3QoVgFO4eJYuClGDS9NXhqk7TWh46/8x8wtZHaw==", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Threading": "4.0.11"}}, "Microsoft.AspNetCore.DataProtection": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "gt4URT+8ljPk0ePspLqOGPJBm+s6iMvsZqweplhf7wiZSjFiG1uYBNpQ/0dFY7wSx3NMRjekyXzCjvkGAV570g==", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "1.0.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "1.0.0", "Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "Microsoft.Win32.Registry": "4.0.0", "System.IO.FileSystem": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Security.Claims": "4.0.1", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Security.Principal.Windows": "4.0.0", "System.Xml.XDocument": "4.0.11"}}, "Microsoft.AspNetCore.DataProtection.Abstractions": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "h5ycDgkqmRdManmYMQVJgzNI7YtVp2X2/os1cKmdfrpfq+m9L8bMKhbd7PCksoLci+aYTOSn45khPl+hpPb9ug==", "dependencies": {"System.ComponentModel": "4.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0"}}, "Microsoft.AspNetCore.Hosting.Abstractions": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "IR2zlm3d/CmYbkw+cMM7M6mUAi+xsFUPfWqGYqzZVC5o6jX3xD2Z4Uf44UBaWKMBf5Z7q9dodIdXxwFPF2Hxhg==", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.0.0", "Microsoft.AspNetCore.Http.Abstractions": "2.0.0", "Microsoft.Extensions.Configuration.Abstractions": "2.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.0.0", "Microsoft.Extensions.Hosting.Abstractions": "2.0.0", "Microsoft.Extensions.Logging.Abstractions": "2.0.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "v2H65ix/O11HKoxhKQpljtozsD5/1tqeXr3TYnrLgfAPIsp6kTFxIcTSENoxtew7h9X14ENqUf2lBCkyCNRUuQ==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.0.0", "Microsoft.Extensions.Configuration.Abstractions": "2.0.0"}}, "Microsoft.AspNetCore.Http": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "c/+eWVWQ8fX5hBHhL1BY4k2n4kVyUnqJLSCj0sTTXwRTU6IKoGbTOUqHT9as8C71Vk54YpAsi/VPmGW7T/ap3A==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "Microsoft.AspNetCore.WebUtilities": "1.0.0", "Microsoft.Extensions.ObjectPool": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "Microsoft.Net.Http.Headers": "1.0.0", "System.Buffers": "4.0.0", "System.Threading": "4.0.11"}}, "Microsoft.AspNetCore.Http.Abstractions": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "pblZLY7IfNqhQ5wwGQ0vNq2mG6W5YgZI1fk7suEuwZsGxGEADNBAyNlTALM9L8nMXdvbp6aHP/t4wHrFpcL3Sw==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.0.0", "System.Text.Encodings.Web": "4.4.0"}}, "Microsoft.AspNetCore.Http.Extensions": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "lA7Bwvur19MhXrlW0w+WBXONJMSFYY5kNazflz4MNwMZMtzwHxNA6fC5sQsssYd/XvA0gMyKwp52s68uuKLR1w==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.0.0", "Microsoft.Net.Http.Headers": "2.0.0", "System.Buffers": "4.4.0"}}, "Microsoft.AspNetCore.Http.Features": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "yk62muzFTZTKCQuo3nmVPkPvGBlM2qbdSxbX62TufuONuKQrTGQ/SwhwBbYutk5/YY7u4HETu0n9BKOn7mMgmA==", "dependencies": {"Microsoft.Extensions.Primitives": "2.0.0"}}, "Microsoft.AspNetCore.JsonPatch": {"type": "Transitive", "resolved": "3.0.0", "contentHash": "LQVXEPxV4jIUiVHHUgBzZxOePSdD7wxV1ndtjg1a76JMqoK3siFka2pVfJXwRq0G4hxbYVWxvamqO733wUhQiA==", "dependencies": {"Microsoft.CSharp": "4.6.0", "Newtonsoft.Json": "12.0.2"}}, "Microsoft.AspNetCore.Mvc.Abstractions": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "d7KEexDwxSwVeZv+SDbsMRPl2WuKMVckOCp/KTGuI1NJhd/7GvNGW101iRIC3tC/yym0PaajcWwTZNVfjhyoJw==", "dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "1.0.0", "Microsoft.CSharp": "4.0.1", "Microsoft.Net.Http.Headers": "1.0.0", "System.ComponentModel.TypeConverter": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Text.Encoding.Extensions": "4.0.11"}}, "Microsoft.AspNetCore.Mvc.ApiExplorer": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "46aWHLmZ37c44bJzLdbSEmIxCwQo7BljHBoK8C9CPCEPOLPWmg0XyPhGyMSGY4woDmm9ukBOEpqT899BWSxhRw==", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": "1.0.0"}}, "Microsoft.AspNetCore.Mvc.Core": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "tjCOZJheOAKStHs4LIcrLsbF/00wEwSinC+vCFpsmdqGVl3/tX9jnID20E1NlkKOW68DOLBavoC23BWFiHa0JA==", "dependencies": {"Microsoft.AspNetCore.Authorization": "1.0.0", "Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.AspNetCore.Http": "1.0.0", "Microsoft.AspNetCore.Mvc.Abstractions": "1.0.0", "Microsoft.AspNetCore.Routing": "1.0.0", "Microsoft.Extensions.DependencyModel": "1.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.PlatformAbstractions": "1.0.0", "System.Buffers": "4.0.0", "System.Diagnostics.DiagnosticSource": "4.0.0", "System.Text.Encoding": "4.0.11"}}, "Microsoft.AspNetCore.Mvc.DataAnnotations": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "ZU02Y2tnKu/lVv2ywnNO+nSRzDWiTlq+ZhSuR9L3Q9NqlCyQJXOgX+iD/BGshnMQ7ZTstjyO4h8WeF7Ii9vBWQ==", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": "1.0.0", "Microsoft.Extensions.Localization": "1.0.0", "System.ComponentModel.Annotations": "4.1.0"}}, "Microsoft.AspNetCore.Mvc.Formatters.Json": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "XQQLbxYLmdRj2U685NxFIrxVxqsXHLO5zN4ZIhTQ23VxzI6Qk2WN9ska0tl4ZMDV/4pSyE8SlmNeKPCN3pW86w==", "dependencies": {"Microsoft.AspNetCore.JsonPatch": "1.0.0", "Microsoft.AspNetCore.Mvc.Core": "1.0.0"}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"type": "Transitive", "resolved": "3.0.0", "contentHash": "euuKDPev8bb87dzyyDd252jxafKkCNCU0Yh1ujLq7DT7g4sUwUsq1bVnow9isaYFFx+DBWSywUcL8ngSra0QHg==", "dependencies": {"Microsoft.AspNetCore.JsonPatch": "3.0.0", "Newtonsoft.Json": "12.0.2", "Newtonsoft.Json.Bson": "1.0.2"}}, "Microsoft.AspNetCore.OpenApi": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "P1mkNhZ3IwU3phNLIUkgqVXb1exnooTalIYwpSON3oKKkcRtACDgS4WpO+xnwFw4KzV0bmgkUqB3acXxIefvvg==", "dependencies": {"Microsoft.OpenApi": "1.4.3"}}, "Microsoft.AspNetCore.Routing": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "vkutCeiBm4ZW1IHpeE2FfYiYgM3oVbAkuKGMjMHlw9AhWrXUAilDIAKL17ECee9295us7tJKP3WpjTdimpzJmA==", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.0.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.0.0", "Microsoft.Extensions.Logging.Abstractions": "2.0.0", "Microsoft.Extensions.ObjectPool": "2.0.0", "Microsoft.Extensions.Options": "2.0.0"}}, "Microsoft.AspNetCore.Routing.Abstractions": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "Kr5Zr8QESkB1Hb234BRZhvhwVgZLcbQtKHQlDPMj/+ZJpbAetKBLW5qWLiQpG4USoa/8kZ8jZtoQ0WcMO2JDag==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.0"}}, "Microsoft.AspNetCore.StaticFiles": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "pXiUBJtpO0fIlGEg/ESykhbIZ2+I+9Y+3qXzN19zZDDF+tD88eATg3A5MHMXu/VmqaROLfvpGJmJ6uOLUGsBVQ==", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.WebEncoders": "1.0.0"}}, "Microsoft.AspNetCore.WebUtilities": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "D0licSnS1JgqQ/gYlN41wXbeYG3dFIdjY781YzMHZ5gBB7kczacshW+H6plZkXRr/cCnAJWGa31o1R8c5GEy/A==", "dependencies": {"Microsoft.Extensions.Primitives": "1.0.0", "System.Buffers": "4.0.0", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Text.Encodings.Web": "4.0.0"}}, "Microsoft.CSharp": {"type": "Transitive", "resolved": "4.6.0", "contentHash": "kxn3M2rnAGy5N5DgcIwcE8QTePWU/XiYcQVzn9HqTls2NKluVzVSmVWRjK7OUPWbljCXuZxHyhEz9kPRIQeXow=="}, "Microsoft.DotNet.PlatformAbstractions": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "l5tDOSom+qpx4pDEoIcqMHnGC7jJ4Uq1DiJ6St/bn0rb5xIh/q4u7OQTIcE1k+1o7E0lYnJA4ZluzS6HGFr4zw==", "dependencies": {"System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}}, "Microsoft.EntityFrameworkCore": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "Vqg/v0cXRQZQH0gY3VHHqt0Bt5WZEkDReVpI1sNYKHVbVtnMYFKPp5SiNygosUJYrJk1rSGb8UVkplDNNUkEgg==", "dependencies": {"Microsoft.Extensions.Caching.Memory": "1.0.0", "Microsoft.Extensions.DependencyInjection": "1.0.0", "Microsoft.Extensions.Logging": "1.0.0", "Remotion.Linq": "2.1.1", "System.Collections.Concurrent": "4.0.12", "System.Collections.Immutable": "1.2.0", "System.ComponentModel.Annotations": "4.1.0", "System.Interactive.Async": "3.0.0", "System.Linq.Queryable": "4.0.1", "System.ObjectModel": "4.0.12", "System.Reflection.Extensions": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0"}}, "Microsoft.Extensions.Caching.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "spsJkYo8gGJapaxTSQFN/wqA+ghpJMLwB4ZyTB+fSdpd7AmMFP/YSpIcGmczcw4KggpxLGhLk7lCkSIlgvHaqQ==", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}}, "Microsoft.Extensions.Caching.Memory": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "6+7zTufCnZ+tfrUo7RbIRR3LB0BxwOwxfXuo0IbLyIvgoToGpWuz5wYEDfCYNOvpig9tY8FA0I1uRHYmITMXMQ==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Linq": "4.1.0", "System.Threading": "4.0.11"}}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "GuQ8+rOJ0juIkv5XZaBBenowg7D7SAsXP/SMEq8nvJPp0Ziy3fekbMDD7EYYovfAxrkHaIx4H1PdTGlLRgqYfw==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "StackExchange.Redis": "2.0.513"}}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "rHFrXqMIvQNq51H8RYTO4IWmDOYh8NUzyqGlh0xHWTP6XYnKk7Ryinys2uDs+Vu88b3AMlM3gBBSs78m6OQpYQ==", "dependencies": {"Microsoft.Extensions.Primitives": "2.0.0"}}, "Microsoft.Extensions.DependencyInjection": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "zdtkiZNV6LB8xtpmfyUjP/9N9ZCL/ydQ+0bfjun38fbrk+MDEm9M2yeLzRdq+OIt5xExw/KU04wFaVwJ1bhQPg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "f9hstgjVmr6rmrfGSpfsVOl2irKAgr1QjrSi3FgnS7kulxband50f2brRLwySAQTADPZeTdow0mpSMcoAdadCw=="}, "Microsoft.Extensions.DependencyModel": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "DyZ/Ibv/SZRMpYhaDCj0nlA+Qe52NyEL51onxAL94bUPauX0jxrK6jyxXN5DI8NVbzE5sOUWZYjTduNqUdbB+g==", "dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.0.0", "Newtonsoft.Json": "9.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Linq": "4.1.0"}}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "Z0AK+hmLO33WAXQ5P1uPzhH7z5yjDHX/XnUefXxE//SyvCb9x4cVjND24dT5566t/yzGp8/WLD7EG9KQKZZklQ==", "dependencies": {"Microsoft.Extensions.Primitives": "2.0.0"}}, "Microsoft.Extensions.FileProviders.Embedded": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "mMCI3/BLXAyBCDneqOI4ohETd0IXjbXZdoiCm1dYdnOdV193ByEOCFQ6/Vn9RVdU5UlC4Nn1P4J5Df7pXG/vGg==", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "System.Runtime.Extensions": "4.1.0"}}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "qPG6Ip/AdHxMJ7j3z8FkkpCbV8yjtiFpf/aOpN3TwfJWbtYpN+BKV8Q+pqPMgk7XZivcju9yARaEVCS++hWopA=="}, "Microsoft.Extensions.Localization": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "nkDgz++GXjMSEIiVS6CpeirV8m8zvc/vUN2sq5sPnqG8PZltCMSNmqrwyL1onx6A6aRNdTr1nVfvYHwWAmS4vg==", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Localization.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Collections.Concurrent": "4.0.12", "System.Resources.Reader": "4.0.0"}}, "Microsoft.Extensions.Localization.Abstractions": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "hQ2sEJf7swsD5jk4DogLI3DazGvsvbz0IuSbxPFDjcvP0PRdxgCsyGpg70LD+3tRmxZcE1uh5jtcAi4X2BcB9w==", "dependencies": {"Microsoft.CSharp": "4.0.1", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1"}}, "Microsoft.Extensions.Logging": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "VP10syWV/vxYYMKgZ2eDESmUsz3gPxvBn5J6tkVN8lI4M+dF43RN8fWsEPbcAneDmZrHl3Pv23z05nmyGkJlpg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.0.0", "Microsoft.Extensions.Logging.Abstractions": "2.0.0", "Microsoft.Extensions.Options": "2.0.0"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "6ZCllUYGFukkymSTx3Yr0G/ajRxoNJp7/FqSxSB4fGISST54ifBhgu4Nc0ItGi3i6DqwuNd8SUyObmiC++AO2Q=="}, "Microsoft.Extensions.ObjectPool": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "drOmgNZCJiNEqFM/TvyqwtogS8wqoWGQCW5KB/CVGKL6VXHw8OOMdaHyspp8HPstP9UDnrnuq+8eaCaAcQg6tA=="}, "Microsoft.Extensions.Options": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "UpZLNLBpIZ0GTebShui7xXYh6DmBHjWM8NxGxZbdQh/bPZ5e6YswqI+bru6BnEL5eWiOdodsXtEz3FROcgi/qg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Primitives": "2.2.0", "System.ComponentModel.Annotations": "4.5.0"}}, "Microsoft.Extensions.PlatformAbstractions": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "zyjUzrOmuevOAJpIo3Mt5GmpALVYCVdLZ99keMbmCxxgQH7oxzU58kGHzE6hAgYEiWsdfMJLjVR7r+vSmaJmtg==", "dependencies": {"System.AppContext": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.Extensions.WebEncoders": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "NSSIBREmHHiyoAFXV2LMA+a6RMZtTHxgUbHJGHRtnjmTKnRyticx5HAzNpy8VG9+HCCHenL9QD7zSA8jjgAi5A==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Text.Encodings.Web": "4.0.0"}}, "Microsoft.IdentityModel.Logging": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "Ol7aYJKsY1UDDTnCjXai17G3bJ0Q7Xj97Ao4nCoSU3+sc1/uMaWhCs3RjsZnrapYbdBPtt1xTPbhC8R6M1MqWQ==", "dependencies": {"System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1"}}, "Microsoft.IdentityModel.Protocols": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "ACezwKCnK5e+SECvwo1l7VWTd6tMdXhg2UPXOAJ7rh/v06q72VBFmc1hb40QEglDVRyV132HBI9zWRfHDCO2Gw==", "dependencies": {"System.Collections.Specialized": "4.0.1", "System.Diagnostics.Contracts": "4.0.1", "System.IdentityModel.Tokens.Jwt": "5.0.0", "System.Net.Http": "4.1.0"}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "JNVtDo5nnrBL15wSFQPuXxDd+bhuqjLHSVpkoEK9j7uKNG6oT1G7jheM8B9zHhcmQAUtnuFabRtZy+HadGBcNw==", "dependencies": {"Microsoft.IdentityModel.Protocols": "2.0.0", "System.Dynamic.Runtime": "4.0.11"}}, "Microsoft.IdentityModel.Tokens": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "EINdydCekJeyG+IyaB/3GbIfEeaOIU5GJiMmpCI+G+o1g9RvRHkS1hpG4KCp3J5nOLzr31k+rRtNrEQcJJEGOQ==", "dependencies": {"Microsoft.IdentityModel.Logging": "1.0.0", "Newtonsoft.Json": "9.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Tools": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0", "System.Security.Claims": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Cng": "4.2.0", "System.Security.Cryptography.Csp": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Text.RegularExpressions": "4.1.0", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11"}}, "Microsoft.Net.Http.Headers": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "Rm9zeNCWyNrGnysHdRXJpNfeDVlPzzFuidSuRLRNvOrnw71vgNPlR4H9wHo2hG/oSaruukqNjK06MDQqb+eXhA==", "dependencies": {"Microsoft.Extensions.Primitives": "2.0.0", "System.Buffers": "4.4.0"}}, "Microsoft.NETCore.Platforms": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "VdLJOCXhZaEMY7Hm2GKiULmn7IEPFE4XC5LPSfBVCUIA8YLZVh846gtfBJalsPQF2PlzdD7ecX7DZEulJ402ZQ=="}, "Microsoft.NETCore.Targets": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg=="}, "Microsoft.OpenApi": {"type": "Transitive", "resolved": "1.4.3", "contentHash": "rURwggB+QZYcSVbDr7HSdhw/FELvMlriW10OeOzjPT7pstefMo7IThhtNtDudxbXhW+lj0NfX72Ka5EDsG8x6w=="}, "Microsoft.Win32.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "+FWlwd//+Tt56316p00hVePBCouXyEzT86Jb3+AuRotTND0IYn0OO3obs1gnQEs/txEnt+rF2JBGLItTG+Be6A==", "dependencies": {"System.Security.AccessControl": "4.5.0", "System.Security.Principal.Windows": "4.5.0"}}, "MimeKit": {"type": "Transitive", "resolved": "0.21.0", "contentHash": "f42v8/z7Tu7Gli7H1V94gZwjLNbkK9lqsKe5TpeKyXE7ajr7y+qp865Qx67Ok4a/PGwoK+UzVpFTE8eMqyEPHQ=="}, "NETStandard.Library": {"type": "Transitive", "resolved": "1.6.1", "contentHash": "WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json": {"type": "Transitive", "resolved": "3.5.8", "contentHash": "Xmri4q9s1NjuiBtOFkWMU9sUZdw5V7kFSf3CQQ3TlDyzWQHo94d5cHNxsSlnezGAZSPfdEG5k7bOrkzTe6rUOQ=="}, "Newtonsoft.Json.Bson": {"type": "Transitive", "resolved": "1.0.2", "contentHash": "QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "dependencies": {"Newtonsoft.Json": "12.0.1"}}, "Pipelines.Sockets.Unofficial": {"type": "Transitive", "resolved": "1.0.7", "contentHash": "n+YKaKVd9ZSa7uVeT0gyOT8eoeCWbiTdpVtnHkjthqmNopVPc4+efRQUReO+WH3WFyVKxgn5QS4BLi2Q+sioqA==", "dependencies": {"System.Buffers": "4.4.0", "System.IO.Pipelines": "4.5.1"}}, "Portable.CommonServiceLocator": {"type": "Transitive", "resolved": "1.2.2", "contentHash": "nJ7hY+rJgpRIDJq1+fuJt0z8XfeGkkKOXO+iqyisDi7yBV4R/tb0seXCptkx1XLbPB+6c41hLtIZG+tCkVQukw=="}, "Remotion.Linq": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "IJn0BqkvwEDpP+2qjvci7n4/a9f7DhKESLWb2/uG4xQh3rTkGTBUz69bI4IivCoKkTFAqjXxYDZw2K/npohjsw==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Linq.Queryable": "4.0.1", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q=="}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA=="}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw=="}, "runtime.native.System": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A=="}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ=="}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ=="}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g=="}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg=="}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ=="}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A=="}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg=="}, "Serilog": {"type": "Transitive", "resolved": "0.1.6", "contentHash": "HOIlDk2pMATBtTkqC7fNTibZvmeufJ9JZyiEWdNQ2bdgVwrVzx2W9V9QKFc0G0qAwNP3hU7eDYNrHcdmcK49+w=="}, "Serilog.AspNetCore": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "YmJnZ2XJeKlDT+pqOL3K++UJyTQY0TQs4VZKy0WebyhoI+/8FFdlHLewP+4Rgi2s06jk33p+NmzqLH45hwrblw==", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.0.0", "Microsoft.Extensions.DependencyInjection": "2.0.0", "Microsoft.Extensions.Logging": "2.0.0", "Serilog": "2.5.0", "Serilog.Extensions.Logging": "2.0.0"}}, "Serilog.Extensions.Logging": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "JAh1vHmbiwDAjA9VmUQjAXvlGm+vDv8usarajlkx7Arm6Zai+CPY4naE4N9M3Eh520wY5GmIWpM+7zmfMbdTOw==", "dependencies": {"Microsoft.Extensions.Logging": "2.0.0", "Serilog": "2.3.0"}}, "StackExchange.Redis": {"type": "Transitive", "resolved": "2.0.513", "contentHash": "Prh45gWLOE1FYHe40sJj+s7Ray31ZEtcBfoNv8C85a59FTY9zKYOv/kPEeOYRV4CKXIa7MGoc0OUlp9xX8jN5g==", "dependencies": {"Pipelines.Sockets.Unofficial": "1.0.7", "System.Diagnostics.PerformanceCounter": "4.5.0", "System.IO.Pipelines": "4.5.1", "System.Threading.Channels": "4.5.0"}}, "Swashbuckle.AspNetCore": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "pYppc+iPifsAYUWi+UoblnjFkUtfP9RZfj5JQaUmEgiWRz5RsCcIuKHvNCGG5AojVKfgjWC4naUPJx4fCjqvDg==", "dependencies": {"NETStandard.Library": "1.6.1", "Swashbuckle.AspNetCore.Swagger": "1.0.0", "Swashbuckle.AspNetCore.SwaggerGen": "1.0.0", "Swashbuckle.AspNetCore.SwaggerUI": "1.0.0"}}, "Swashbuckle.AspNetCore.Newtonsoft": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "mYmLNCBX/5BpEuMgSmzsI4jFMQxaYcwSk5WxCGCwRHc+pYzjQsm22eXdeycrkGIh5TrAOv+wtjK8AZOoWCR3BQ==", "dependencies": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson": "3.0.0", "Swashbuckle.AspNetCore.SwaggerGen": "5.0.0"}}, "Swashbuckle.AspNetCore.Swagger": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "tzZ6tXjopV6jvcHzOX7PjyYBWqof5mhJUXqxb/kPF17Z5LXxr+uglnTLlYOjWCm5BfvdxWYtU95uJgc14L1Eqw==", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": "1.0.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "1.0.0", "NETStandard.Library": "1.6.1"}}, "Swashbuckle.AspNetCore.SwaggerGen": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "1kuKS+4x1m85gpTOqICp5BRZB3Ir6VsegPN9hanH6xRp+tGDQNfFhVNPZ59Wrl8gM8YqIhB85xmFAkK68CqVdA==", "dependencies": {"Microsoft.AspNetCore.Mvc.ApiExplorer": "1.0.0", "Microsoft.AspNetCore.Mvc.Core": "1.0.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "1.0.0", "NETStandard.Library": "1.6.1", "Swashbuckle.AspNetCore.Swagger": "1.0.0", "System.Xml.XPath": "4.0.1"}}, "Swashbuckle.AspNetCore.SwaggerUI": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "syirndQQePJXF8Dgzgjqt0AoxWVguPQy/p4siHNACBSGicxvk6AAd1ULIFE83ANJOCl2GmXuAvNAZBMXGbxK9A==", "dependencies": {"Microsoft.AspNetCore.Routing": "1.0.0", "Microsoft.AspNetCore.StaticFiles": "1.0.0", "Microsoft.Extensions.FileProviders.Embedded": "1.0.0", "NETStandard.Library": "1.6.1", "Newtonsoft.Json": "9.0.1", "System.Xml.XPath": "4.0.0"}}, "System.AppContext": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers": {"type": "Transitive", "resolved": "4.4.0", "contentHash": "AwarXzzoDwX6BgrhjoJsk6tUezZEozOT5Y9QKF94Gl4JK91I4PIIBkBco9068Y9/Dra8Dkbie99kXB8+1BaYKw=="}, "System.Collections": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable": {"type": "Transitive", "resolved": "1.2.0", "contentHash": "Cma8cBW6di16ZLibL8LYQ+cLjGzoKxpOTu/faZfDcx94ZjAGq6Nv5RO7+T1YZXqEXTZP9rt1wLVEONVpURtUqw==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Collections.NonGeneric": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "hMxFT2RhhlffyCdKLDXjx8WEC5JfCvNozAZxCablAuFRH74SCV4AgzE8yJCh/73bFnEoZgJ9MJmkjQ0dJmnKqA==", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Collections.Specialized": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "/HKQyVP0yH1I0YtK7KJL/28snxHNH/bi+0lgk/+MbURF6ULhAE31MDI+NZDerNWu264YbxklXCCygISgm+HMug==", "dependencies": {"System.Collections.NonGeneric": "4.0.1", "System.Globalization": "4.0.11", "System.Globalization.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.ComponentModel": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "oBZFnm7seFiVfugsIyOvQCWobNZs7FzqDV/B7tx20Ep/l3UUFCPDkdTnCNaJZTU27zjeODmy2C/cP60u3D4c9w==", "dependencies": {"System.Runtime": "4.1.0"}}, "System.ComponentModel.Annotations": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "UxYQ3FGUOtzJ7LfSdnYSFd7+oEv6M8NgUatatIN2HxNtDdlcvFAf+VIq4Of9cDMJEJC0aSRv/x898RYhB4Yppg=="}, "System.ComponentModel.Primitives": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "sc/7eVCdxPrp3ljpgTKVaQGUXiW05phNWvtv/m2kocXqrUQvTVWKou1Edas2aDjTThLPZOxPYIGNb/HN0QjURg==", "dependencies": {"System.ComponentModel": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0"}}, "System.ComponentModel.TypeConverter": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "MnDAlaeJZy9pdB5ZdOlwdxfpI+LJQ6e0hmH7d2+y2LkiD8DRJynyDYl4Xxf3fWFm7SbEwBZh4elcfzONQLOoQw==", "dependencies": {"System.Collections": "4.0.11", "System.Collections.NonGeneric": "4.0.1", "System.Collections.Specialized": "4.0.1", "System.ComponentModel": "4.0.1", "System.ComponentModel.Primitives": "4.1.0", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Configuration.ConfigurationManager": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "UIFvaFfuKhLr9u5tWMxmVoDPkFeD+Qv8gUuap4aZgVGYSYMdERck4OhLN/2gulAc0nYTEigWXSJNNWshrmxnng==", "dependencies": {"System.Security.Cryptography.ProtectedData": "4.5.0", "System.Security.Permissions": "4.5.0"}}, "System.Console": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Contracts": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "HvQQjy712vnlpPxaloZYkuE78Gn353L0SJLJVeLcNASeg9c4qla2a1Xq8I7B3jZoDzKPtHTkyVO7AZ5tpeQGuA==", "dependencies": {"System.Runtime": "4.1.0"}}, "System.Diagnostics.Debug": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "tD6kosZnTAGdrEa0tZSuFyunMbt/5KYDnHdndJYGqZoNy00XVXyACd5d6KnE1YgYv3ne2CjtAfNXo/fwEhnKUA==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Diagnostics.PerformanceCounter": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "JUO5/moXgchWZBMBElgmPebZPKCgwW8kY3dFwVJavaNR2ftcc/YjXXGjOaCjly2KBXT7Ld5l/GTkMVzNv41yZA==", "dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.Win32.Registry": "4.5.0", "System.Configuration.ConfigurationManager": "4.5.0", "System.Security.Principal.Windows": "4.5.0"}}, "System.Diagnostics.Tools": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Tracing": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Dynamic.Runtime": {"type": "Transitive", "resolved": "4.0.11", "contentHash": "db34f6LHYM0U0JpE+sOmjar27BnqTVkbLJhgfwMpTdgTigG/Hna3m2MYVwnFzGGKnEJk2UXFuoVTr8WUbU91/A==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Globalization": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "Z5pHOvIGFbyID9j4te1kpJD0Km/botsFvf++IemZqQsMwKeCa5O25dS20XzIK5rbW04gtv03ObCxitrxK3vfMQ=="}, "System.Interactive.Async": {"type": "Transitive", "resolved": "3.0.0", "contentHash": "iyrgkZz9Dzm0fiPouQszFC3SO/46k6AYd/jG9bu+/o0AoDMaRXtlo3TIuWVNtOuJFd1noL963QouroJ0T3rImw==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}}, "System.IO": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "dependencies": {"System.Buffers": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Pipelines": {"type": "Transitive", "resolved": "4.5.1", "contentHash": "oY5m31iOIZhvW0C69YTdKQWIbiYjFLgxn9NFXA7XeWD947uEk0zOi9fLbGtYgbs1eF7kTQ4zl9IeGQHthz+m+A=="}, "System.Linq": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "Yn/WfYe9RoRfmSLvUt2JerP0BTGGykCZkQPgojaxgzF2N0oPo+/AhB8TXOpdCcNlrG3VRtsamtK2uzsp3cqRVw==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Net.Http": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Net.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.ObjectModel": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.Reader": {"type": "Transitive", "resolved": "4.0.0", "contentHash": "VX1iHAoHxgrLZv+nq/9drCZI6Q4SSCzSVyUm1e0U60sqWdj6XhY7wvKmy3RvsSal9h+/vqSWwxxJsm0J4vn/jA==", "dependencies": {"System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11"}}, "System.Resources.ResourceManager": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg=="}, "System.Runtime.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Primitives": {"type": "Transitive", "resolved": "4.1.1", "contentHash": "HZ6Du5QrTG8MNJbf4e4qMO3JRAkIboGT5Fk804uZtg3Gq516S7hAqTm2UZKUHa7/6HUGdVy3AqMQKbns06G/cg==", "dependencies": {"System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Security.AccessControl": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "vW8Eoq0TMyz5vAG/6ce483x/CP83fgm4SJe5P8Tb1tZaobcvPrbMEL7rhH1DRdrYbbb6F0vq3OlzmK0Pkwks5A==", "dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Security.Principal.Windows": "4.5.0"}}, "System.Security.Claims": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "4Jlp0OgJLS/Voj1kyFP6MJlIYp3crgfH8kNQk2p7+4JYfc1aAmh9PZyAMMbDhuoolGNtux9HqSOazsioRiDvCw==", "dependencies": {"System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Security.Principal": "4.0.1"}}, "System.Security.Cryptography.Algorithms": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "wLBKzFnDCxP12VL9ANydSYhk59fC4cvOr9ypYQLPnAj48NQIhqnjdD2yhP8yEKyBJEjERWS9DisKL7rX5eU25Q=="}, "System.Security.Cryptography.X509Certificates": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Permissions": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "9gdyuARhUR7H+p5CjyUB/zPk7/Xut3wUSP8NJQB6iZr8L3XUXTMdoLeVAg9N4rqF8oIpE7MpdqHdDHQ7XgJe0g==", "dependencies": {"System.Security.AccessControl": "4.5.0"}}, "System.Security.Principal": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "On+SKhXY5rzxh/S8wlH1Rm0ogBlu7zyHNxeNBiXauNrhHRXAe9EuX8Yl5IOzLPGU5Z4kLWHMvORDOCG8iu9hww==", "dependencies": {"System.Runtime": "4.1.0"}}, "System.Security.Principal.Windows": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "U77HfRXlZlOeIXd//Yoj6Jnk8AXlbeisf1oq1os+hxOGVnuG+lGSfGqTwTZBoORFF6j/0q7HXIl8cqwQ9aUGqQ==", "dependencies": {"Microsoft.NETCore.Platforms": "2.0.0"}}, "System.Text.Encoding": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web": {"type": "Transitive", "resolved": "4.4.0", "contentHash": "l/tYeikqMHX2MD2jzrHDfR9ejrpTTF7wvAEbR51AMvzip1wSJgiURbDik4iv/w7ZgytmTD/hlwpplEhF9bmFNw=="}, "System.Text.RegularExpressions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "MEH06N0rIGmRT4LOKQ2BmUO0IxfvmIY/PaouSq+DFQku72OL8cxfw8W99uGpTCFf2vx2QHLRSh374iSM3asdTA=="}, "System.Threading.Tasks": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "npvJkVKl5rKXrtl1Kkm6OhOUaYGEiF9wFbppFRWSMoApKzt2PiPHT2Bb8a5sAWxprvdOAtvaARS9QYMznEUtug==", "dependencies": {"System.Collections": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Timer": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.ValueTuple": {"type": "Transitive", "resolved": "4.4.0", "contentHash": "BahUww/+mdP4ARCAh2RQhQTg13wYLVrBb9SYVgW8ZlrwjraGCXHGjo0oIiUfZ34LUZkMMR+RAzR7dEY4S1HeQQ=="}, "System.Xml.ReaderWriter": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.3.0"}}, "System.Xml.XDocument": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XPath": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "UWd1H+1IJ9Wlq5nognZ/XJdyj8qPE4XufBUkAW59ijsCPjZkZe0MUzKKJFBr+ZWBe5Wq1u1d5f2CYgE93uH7DA==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11"}}, "contract": {"type": "Project", "dependencies": {"Ardalis.SmartEnum": "(, )", "Asp.Versioning.Mvc.ApiExplorer": "(, )", "Carter": "(, )", "CloudinaryDotNet": "(, )", "FluentValidation": "(, )", "FluentValidation.DependencyInjectionExtensions": "(, )", "MailKit": "(, )", "MediatR": "(, )", "Microsoft.AspNetCore.Authentication.Google": "(, )", "Microsoft.AspNetCore.Authentication.JwtBearer": "(, )", "Microsoft.AspNetCore.OpenApi": "(, )", "Microsoft.EntityFrameworkCore": "(, )", "Microsoft.Extensions.Caching.StackExchangeRedis": "(, )", "Microsoft.Extensions.DependencyInjection": "(, )", "Microsoft.IdentityModel.Tokens": "(, )", "Newtonsoft.Json": "(, )", "Serilog": "(, )", "Serilog.AspNetCore": "(, )", "Swashbuckle.AspNetCore": "(, )", "Swashbuckle.AspNetCore.Newtonsoft": "(, )", "Swashbuckle.AspNetCore.SwaggerGen": "(, )", "System.IdentityModel.Tokens.Jwt": "(, )"}}}}}