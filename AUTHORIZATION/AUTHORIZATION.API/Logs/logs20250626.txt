2025-06-26 11:02:20.285 +07:00 [WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductTag'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-26 11:02:20.316 +07:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-26 12:53:15.906 +07:00 [WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductTag'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-26 12:53:15.955 +07:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-26 13:10:36.422 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:10:45.081 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:31:47.720 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:31:52.816 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:37:39.529 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:37:43.744 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:37:48.762 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:37:54.993 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:42:57.617 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:42:58.997 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:43:00.816 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:43:01.997 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:43:13.035 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:50:09.786 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:50:12.766 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:50:14.158 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:51:23.598 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:53:01.572 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:53:02.373 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:53:02.945 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:53:03.101 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:57:21.443 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:57:25.134 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:58:17.594 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:58:27.747 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 13:58:30.033 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:01:44.277 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 15
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:02:30.116 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:02:49.553 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:03:09.785 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:03:14.198 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:03:52.619 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:03:54.456 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:04:13.527 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:04:20.597 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:09:16.606 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:09:29.001 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:10:35.895 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:10:37.489 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:10:50.562 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:10:52.867 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:11:09.980 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:11:10.877 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:11:22.500 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:11:39.915 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:11:44.899 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:12:01.216 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:12:01.908 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:12:02.088 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:12:14.307 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:12:23.398 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:12:32.049 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:12:32.574 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:12:32.777 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:12:32.980 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:12:43.775 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:12:44.579 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:12:44.781 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:14:29.725 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:14:36.938 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:14:37.844 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:14:38.306 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:14:44.901 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:14:45.529 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:14:45.720 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:15:02.577 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:15:06.208 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:15:06.734 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:15:15.477 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:15:24.655 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:15:25.816 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:15:30.485 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:15:31.259 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:15:31.450 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:15:49.443 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:15:49.989 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:15:50.170 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:15:50.405 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:15.607 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:22.054 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:30.401 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:30.984 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:31.208 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:42.797 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:43.336 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:43.527 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:43.855 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:44.045 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:50.142 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:51.226 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:51.689 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:51.867 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:16:52.058 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:17:05.259 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:17:14.804 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:17:15.534 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:17:15.737 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:17:23.039 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:17:45.654 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:17:46.358 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:17:46.562 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:22:26.486 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:22:29.188 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:22:37.099 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:22:38.033 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:22:38.337 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:23:17.948 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:24:06.020 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:24:16.371 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:24:21.636 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:24:29.386 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:24:30.149 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:24:30.342 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:25:00.628 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:25:02.766 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:25:03.227 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:25:11.707 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:25:20.111 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:25:23.590 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:25:30.891 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:25:44.310 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:25:47.234 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:25:51.194 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:26:16.633 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:26:25.889 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:26:59.222 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:27:23.208 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:27:24.962 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:27:41.390 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:27:58.489 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:28:03.424 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:28:14.383 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:31:13.247 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:32:41.966 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:33:34.546 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:33:44.862 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:33:51.016 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:34:00.724 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:34:04.504 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:34:08.745 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:34:13.143 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:34:40.460 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:34:43.147 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:34:46.882 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:37:10.635 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:38:27.013 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:38:36.800 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:38:45.506 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:39:25.557 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:39:35.557 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:40:26.542 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:40:49.469 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:43:53.537 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:44:23.169 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:45:26.674 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:45:30.986 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:45:34.054 +07:00 [ERR] The provided password does not match the user's password
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+PasswordMismatch: The provided password does not match the user's password
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 26
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:45:39.329 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:45:41.318 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:45:52.412 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:46:14.971 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:46:15.810 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:46:16.330 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:46:21.687 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:46:22.403 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:46:22.629 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:46:22.808 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:46:41.339 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:46:42.091 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:46:42.283 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:46:42.474 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:46:42.653 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:46:50.272 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:47:33.696 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 14:56:48.567 +07:00 [ERR] User not found
CONTRACT.CONTRACT.DOMAIN.Exceptions.UserException+NotFound: User not found
   at AUTHORIZATION.APPLICATION.UseCases.Query.LoginQueryHandler.Handle(LoginQuery request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.APPLICATION\UseCases\Query\LoginQueryHandler.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at AUTHORIZATION.PRESENTATION.Apis.UserApis.Login(ISender sender, LoginQuery query) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.PRESENTATION\Apis\UserApis.cs:line 25
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AUTHORIZATION.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\AUTHORIZATION\AUTHORIZATION.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-26 15:24:15.273 +07:00 [WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductTag'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-26 15:24:15.325 +07:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-26 15:32:45.779 +07:00 [WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductTag'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-26 15:32:45.835 +07:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
