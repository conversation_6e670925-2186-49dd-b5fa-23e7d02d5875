<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" />
        <PackageReference Include="Swashbuckle.AspNetCore" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" />
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations" />
        <PackageReference Include="MicroElements.Swashbuckle.FluentValidation" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\COMMAND\COMMAND.API\COMMAND.API.csproj" />
        <ProjectReference Include="..\..\COMMAND\COMMAND.APPLICATION\COMMAND.APPLICATION.csproj" />
        <ProjectReference Include="..\..\COMMAND\COMMAND.INFRASTRUCTURE\COMMAND.INFRASTRUCTURE.csproj" />
        <ProjectReference Include="..\..\CONTRACT\CONTRACT\CONTRACT.csproj"/>
        <ProjectReference Include="..\..\E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults\ServiceDefaults.csproj"/>
        <ProjectReference Include="..\AUTHORIZATION.APPLICATION\AUTHORIZATION.APPLICATION.csproj"/>
        <ProjectReference Include="..\AUTHORIZATION.CONTRACT\AUTHORIZATION.CONTRACT.csproj"/>
        <ProjectReference Include="..\AUTHORIZATION.INFRASTRUCTURE\AUTHORIZATION.INFRASTRUCTURE.csproj"/>
        <ProjectReference Include="..\AUTHORIZATION.PRESENTATION\AUTHORIZATION.PRESENTATION.csproj"/>
    </ItemGroup>

</Project>
