<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <Folder Include="CONTRACT.API\"/>
        <Folder Include="CONTRACT.APPLICATION\"/>
        <Folder Include="CONTRACT.CONTRACT\Services\"/>
        <Folder Include="CONTRACT.INFRASTRUCTURE\DependencyInjection\Extensions\"/>
        <Folder Include="CONTRACT.PRESENTATION\"/>
        <Folder Include="CONTRACT.PERSISTENCE\"/>
        <Folder Include="CONTRACT.INFRASTRUCTURE\"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Carter" />
        <PackageReference Include="Ardalis.SmartEnum" />
        <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" />
        <PackageReference Include="CloudinaryDotNet" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" />

        <PackageReference Include="FluentValidation" />
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
        <PackageReference Include="MailKit" />
        <PackageReference Include="MediatR" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" />
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
        <PackageReference Include="Microsoft.IdentityModel.Tokens" />
        <PackageReference Include="Newtonsoft.Json" />
        <PackageReference Include="Serilog" />
        <PackageReference Include="Serilog.AspNetCore" />
        <PackageReference Include="Swashbuckle.AspNetCore" />
        <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
    </ItemGroup>

</Project>
